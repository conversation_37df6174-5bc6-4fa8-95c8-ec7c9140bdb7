# COCO-Stuff-164K

<!-- [ALGORITHM] -->

## Introduction

The Common Objects in COntext-stuff (COCO-stuff) dataset is a dataset for scene understanding tasks like semantic segmentation, object detection and image captioning. It is constructed by annotating the original COCO dataset, which originally annotated things while neglecting stuff annotations.  There are 164k images in COCO-Stuff-164K dataset that span over 172 categories including 80 things, 91 stuff, and 1 unlabeled class.

## Results and Models

| Method      | Backbone      | Pretrain                                                                                                                  | BS | Lr schd | Crop | mIoU (SS/MS)                                                                                                                                                                            | #Param | Config                                                                   | Download                                                                                                                                                                                                                        |
|:-----------:|:-------------:|:--------------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:------:|:------------------------------------------------------------------------:|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| UperNet     | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth)      | 8x2        | 80k     | 640       | [50.5](https://drive.google.com/file/d/1CninnhxkN3VDhmeOhhcg_K72ZG1hyW3x/view?usp=sharing) / [50.7](https://drive.google.com/file/d/1RUTAoL95giuG0vy-0nvkLIoUB7RZlh9V/view?usp=sharing) | 451M   | [config](./upernet_beit_adapter_large_640_80k_cocostuff164k_ss.py)       | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.6/upernet_beit_adapter_large_640_80k_cocostuff164k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_beit_adapter_large_640_80k_cocostuff164k_ss.log)   |
| Mask2Former | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth)      | 16x1       | 80k     | 896       | [51.7](https://drive.google.com/file/d/1n6fekFr6Kr69g5kTBPwkPfa6HbaBG4TC/view?usp=sharing) / [52.0](https://drive.google.com/file/d/1ED4l-2n1P2K2SplZ1JKvwja_uzaEuU1l/view?usp=sharing) | 571M   | [config](./mask2former_beit_adapter_large_896_80k_cocostuff164k_ss.py)   | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.6/mask2former_beit_adapter_large_896_80k_cocostuff164k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_896_80k_cocostuff164k_ss.log)   |
| Mask2Former | ViT-Adapter-L | [BEiTv2-L](https://conversationhub.blob.core.windows.net/beit-share-public/beitv2/beitv2_large_patch16_224_pt1k_ft21k.pth) | 16x1       | 80k     | 896       | 52.3 / -                                                                                                                                                                                | 571M   | [config](./mask2former_beitv2_adapter_large_896_80k_cocostuff164k_ss.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/mask2former_beitv2_adapter_large_896_80k_cocostuff164k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beitv2_adapter_large_896_80k_cocostuff164k_ss.log) |
