# Cityscapes

<!-- [ALGORITHM] -->

## Introduction

Cityscapes is a large-scale database which focuses on semantic understanding of urban street scenes. It provides semantic, instance-wise, and dense pixel annotations for 30 classes grouped into 8 categories (flat surfaces, humans, vehicles, constructions, objects, nature, sky, and void). The dataset consists of around 5000 fine annotated images and 20000 coarse annotated ones. Data was captured in 50 cities during several months, daytimes, and good weather conditions. It was originally recorded as video so the frames were manually selected to have the following features: large number of dynamic objects, varying scene layout, and varying background.

## Results and Models

Cityscapes val set

| Method      | Backbone      | Pretrain                                                                                                                         | BS | Lr schd | Crop | mIoU (SS/MS)                                                                                                                                                                            | #Param | Config                                                              | Download                                                                                                                                                                                                                                           |
|:-----------:|:-------------:|:--------------------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:------:|:-------------------------------------------------------------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| Mask2Former | ViT-Adapter-L | [Mapillary](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.3/mask2former_beit_adapter_large_896_80k_mapillary.zip) | 16x1       | 80k     | 896       | [84.9](https://drive.google.com/file/d/1LKy0zz-brCBbKGmUWquadILaBHdDLR6s/view?usp=sharing) / [85.8](https://drive.google.com/file/d/1LSJvK1BPSbzm9eWpKL8Xo7RmYBrd2xux/view?usp=sharing) | 571M   | [config](./mask2former_beit_adapter_large_896_80k_cityscapes_ss.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.3/mask2former_beit_adapter_large_896_80k_cityscapes.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_896_80k_cityscapes_ss.log) |

- Note that the [Mapillary](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.3/mask2former_beit_adapter_large_896_80k_mapillary.zip) pretrained weights should be loaded by using `--cfg-options load_from=<pretrained_path>`

Cityscapes test set

| Method      | Backbone      | Pretrain                                                                                                                         | BS | Lr schd | Crop | mIoU (SS/MS)                                                                                                                          | #Param | Config                                                              | Download                                                                                                                                                                                                                                           |
|:-----------:|:-------------:|:--------------------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:-------------------------------------------------------------------------------------------------------------------------------------:|:------:|:-------------------------------------------------------------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| Mask2Former | ViT-Adapter-L | [Mapillary](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.3/mask2former_beit_adapter_large_896_80k_mapillary.zip) | 16x1       | 80k     | 896       | - / [85.2](https://www.cityscapes-dataset.com/anonymous-results/?id=0ca6821dc3183ff970bd5266f812df2eaa4519ecb1973ca1308d65a3b546bf27) | 571M   | [config](./mask2former_beit_adapter_large_896_80k_cityscapes_ss.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.3/mask2former_beit_adapter_large_896_80k_cityscapes.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_896_80k_cityscapes_ss.log) |
