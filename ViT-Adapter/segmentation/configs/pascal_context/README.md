# Pascal Context

<!-- [ALGORITHM] -->

## Introduction

The PASCAL Context dataset is an extension of the PASCAL VOC 2010 detection challenge, and it contains pixel-wise labels for all training images. It contains more than 400 classes (including the original 20 classes plus backgrounds from PASCAL VOC segmentation), divided into three categories (objects, stuff, and hybrids). Many of the object categories of this dataset are too sparse and; therefore, a subset of 59 frequent classes are usually selected for use.

## Results and Models

| Method      | Backbone      | Pretrain                                                                                                             | BS | Lr schd | Crop | mIoU (SS/MS)                                                                                                                                                                            | #Param | Config                                                                     | Download                                                                                                                                                                                                                             |
|:-----------:|:-------------:|:---------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:------:|:--------------------------------------------------------------------------:|:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| Mask2Former | ViT-Adapter-B | [BEiT-B](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_base_patch16_224_pt22k_ft22k.pth)  | 8x2        | 40k     | 480       | 64.0 / 64.4                                                                                                                                                                             | 120M   | [config](./mask2former_beit_adapter_base_480_40k_pascal_context_59_ss.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.8/mask2former_beit_adapter_base_480_40k_pascal_context_59.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_base_480_40k_pascal_context_59_ss.log) |
| UperNet     | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth) | 8x2        | 80k     | 480       | [67.0](https://drive.google.com/file/d/1BxnwkxGZzncpd_G4cDmHPB6Pq681YogD/view?usp=sharing) / [67.5](https://drive.google.com/file/d/1Ff-6CGyNs5_ORrlLnyYkV9spE59UjwiT/view?usp=sharing) | 451M   | [config](./upernet_beit_adapter_large_480_80k_pascal_context_59_ss.py)     | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.7/upernet_beit_adapter_large_480_80k_pascal_context_59.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_beit_adapter_large_480_80k_pascal_context_59_ss.log)    |
| Mask2Former | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth) | 8x2        | 40k     | 480       | [67.8](https://drive.google.com/file/d/1AbC7DZeTjZVIqNTRWFCkc16FHEmxvDNK/view?usp=sharing) / [68.2](https://drive.google.com/file/d/1gl_gPF7pxjWKvUubK7g1CB5wtihuAWgA/view?usp=sharing) | 568M   | [config](./mask2former_beit_adapter_large_480_40k_pascal_context_59_ss.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.7/mask2former_beit_adapter_large_480_40k_pascal_context_59.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_480_40k_pascal_context_59_ss.log)    |
