# ADE20K

<!-- [ALGORITHM] -->

## Introduction

The ADE20K semantic segmentation dataset contains more than 20K scene-centric images exhaustively annotated with pixel-level objects and object parts labels. There are totally 150 semantic categories, which include stuffs like sky, road, grass, and discrete objects like person, car, bed.

## Results and Models

There are two training strategies for the ADE20K dataset:

- `strategy 1`: ADE20K 160k iterations.

- `strategy 2`: COCO-Stuff 80k + ADE20K 80k iterations.

In other words, if the filename contains `80k`, that means `strategy 2`.

**DeiT Pretrain (ImageNet-1K, supervised)**

| Method  | Backbone      | Pretrain                                                                         | Batch Size | Lr schd | Crop Size | mIoU (SS/MS) | #Param | Config                                                    | Download                                                                                                                                                                                                                            |
|:-------:|:-------------:|:---------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:------------:|:------:|:---------------------------------------------------------:|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| UperNet | ViT-Adapter-T | [DeiT-T](https://dl.fbaipublicfiles.com/deit/deit_tiny_patch16_224-a1311bcf.pth)  | 8x2        | 160k    | 512       | 42.6 / 43.6  | 36M    | [config](./upernet_deit_adapter_tiny_512_160k_ade20k.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_deit_adapter_tiny_512_160_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_deit_adapter_tiny_512_160k_ade20k.log)  |
| UperNet | ViT-Adapter-S | [DeiT-S](https://dl.fbaipublicfiles.com/deit/deit_small_patch16_224-cd65a155.pth) | 8x2        | 160k    | 512       | 46.2 / 47.1  | 58M    | [config](./upernet_deit_adapter_small_512_160k_ade20k.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_deit_adapter_small_512_160k_ade20k.pth)                                                                                                               |
| UperNet | ViT-Adapter-B | [DeiT-B](https://dl.fbaipublicfiles.com/deit/deit_base_patch16_224-b5f2ef4d.pth)  | 8x2        | 160k    | 512       | 48.8 / 49.7  | 134M   | [config](./upernet_deit_adapter_base_512_160k_ade20k.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_deit_adapter_base_512_160k_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_deit_adapter_base_512_160k_ade20k.log) |

**AugReg Pretrain (ImageNet-22K, supervised)**

| Method  | Backbone      | Pretrain                                                                                                                                                                        | Batch Size | Lr schd | Crop Size | mIoU (SS/MS) | #Param | Config                                                      | Download                                                                                                                                                                                                                                  |
|:-------:|:-------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:------------:|:------:|:-----------------------------------------------------------:|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| UperNet | ViT-Adapter-T | [AugReg-T](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/Ti_16-i21k-300ep-lr_0.001-aug_none-wd_0.03-do_0.0-sd_0.0--imagenet2012-steps_20k-lr_0.03-res_224.pth)  | 8x2        | 160k    | 512       | 43.9 / 44.8  | 36M    | [config](./upernet_augreg_adapter_tiny_512_160k_ade20k.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_augreg_adapter_tiny_512_160_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_augreg_adapter_tiny_512_160_ade20k.log)     |
| UperNet | ViT-Adapter-B | [AugReg-B](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/B_16-i21k-300ep-lr_0.001-aug_medium1-wd_0.1-do_0.0-sd_0.0--imagenet2012-steps_20k-lr_0.01-res_384.pth) | 8x2        | 160k    | 512       | 51.9 / 52.5  | 134M   | [config](./upernet_augreg_adapter_base_512_160k_ade20k.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_augreg_adapter_base_512_160k_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_augreg_adapter_base_512_160k_ade20k.log)   |
| UperNet | ViT-Adapter-L | [AugReg-L](https://github.com/czczup/ViT-Adapter/releases/download/v0.1.6/L_16-i21k-300ep-lr_0.001-aug_medium1-wd_0.1-do_0.1-sd_0.1--imagenet2012-steps_20k-lr_0.01-res_384.pth) | 8x2        | 160k    | 512       | 53.4 / 54.4  | 364M   | [config](./upernet_augreg_adapter_large_512_160k_ade20k.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/upernet_augreg_adapter_large_512_160k_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_augreg_adapter_large_512_160k_ade20k.log) |

**BEiT Pretrain (ImageNet-22K, MIM)**

| Method      | Backbone      | Pretrain                                                                                                                                        | BS | Lr schd | Crop | mIoU (SS/MS)                                                                                                                                                                            | #Param | Config                                                             | Download                                                                                                                                                                                                                                       |
|:-----------:|:-------------:|:------------------------------------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:------:|:------------------------------------------------------------------:|:----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| UperNet     | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth)                            | 8x2        | 160k    | 640       | [58.0](https://drive.google.com/file/d/1KsV4QPfoRi5cj2hjCzy8VfWih8xCTrE3/view?usp=sharing) / [58.4](https://drive.google.com/file/d/1haeTUvQhKCM7hunVdK60yxULbRH7YYBK/view?usp=sharing) | 451M   | [config](./upernet_beit_adapter_large_640_160k_ade20k_ss.py)       | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.1/upernet_beit_adapter_large_640_160k_ade20k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_beit_adapter_large_640_160k_ade20k_ss.log)       |
| Mask2Former | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth)                            | 8x2        | 160k    | 640       | [58.3](https://drive.google.com/file/d/1jj56lSbc2s4ZNc-Hi-w6o-OSS99oi-_g/view?usp=sharing) / [59.0](https://drive.google.com/file/d/1hgpZB5gsyd7LTS7Aay2CbHmlY10nafCw/view?usp=sharing) | 568M   | [config](./mask2former_beit_adapter_large_640_160k_ade20k_ss.py)   | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.2/mask2former_beit_adapter_large_640_160k_ade20k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_640_160k_ade20k_ss.log)   |
| Mask2Former | ViT-Adapter-L | [BEiT-L+COCO](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.6/mask2former_beit_adapter_large_896_80k_cocostuff164k.zip)     | 16x1       | 80k     | 896       | [59.4](https://drive.google.com/file/d/1B_1XSwdnLhjJeUmn1g_nxfvGJpYmYWHa/view?usp=sharing) / [60.5](https://drive.google.com/file/d/1UtjmgcYKR-2h116oQXklUYOVcTw15woM/view?usp=sharing) | 571M   | [config](./mask2former_beit_adapter_large_896_80k_ade20k_ss.py)    | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.0/mask2former_beit_adapter_large_896_80k_ade20k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_896_80k_ade20k_ss.log)     |
| Mask2Former | ViT-Adapter-L | BEiTv2                                                                                                                                           | 16x1       | 160k    | 896       | - / -                                                                                                                                                                                   | 571M   | [config](./mask2former_beitv2_adapter_large_896_160k_ade20k_ss.py) | -                                                                                                                                                                                                                                              |
| Mask2Former | ViT-Adapter-L | [BEiTv2-L+COCO](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/mask2former_beitv2_adapter_large_896_80k_cocostuff164k.zip) | 16x1       | 80k     | 896       | 61.2 / 61.5                                                                                                                                                                             | 571M   | [config](./mask2former_beitv2_adapter_large_896_80k_ade20k_ss.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.3.1/mask2former_beitv2_adapter_large_896_80k_ade20k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beitv2_adapter_large_896_80k_ade20k_ss.log) |
