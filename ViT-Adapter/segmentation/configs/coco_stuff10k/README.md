# COCO-Stuff-10K

<!-- [ALGORITHM] -->

## Introduction

The Common Objects in COntext-stuff (COCO-stuff) dataset is a dataset for scene understanding tasks like semantic segmentation, object detection and image captioning. It is constructed by annotating the original COCO dataset, which originally annotated things while neglecting stuff annotations. There are 10k images in COCO-Stuff-10K dataset that span over 172 categories including 80 things, 91 stuff, and 1 unlabeled class. It is split into 9,000 and 1,000 images for training and testing.

## Results and Models

| Method      | Backbone      | Pretrain                                                                                                             | Batch Size | Lr schd | Crop | mIoU (SS/MS)                                                                                                                                                                            | #Param | Config                                                                | Download                                                                                                                                                                                                                                                 |
|:-----------:|:-------------:|:---------------------------------------------------------------------------------------------------------------------:|:----------:|:-------:|:---------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:------:|:---------------------------------------------------------------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| Mask2Former | ViT-Adapter-B | [BEiT-B](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_base_patch16_224_pt22k_ft22k.pth)  | 8x2        | 40k     | 512       | 50.0 / 50.5                                                                                                                                                                             | 120M   | [config](./mask2former_beit_adapter_base_512_40k_cocostuff10k_ss.py)  | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.9/mask2former_beit_adapter_base_512_40k_cocostuff10k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_base_512_40k_cocostuff10k_ss.log) |
| UperNet     | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth) | 8x2        | 80k     | 512       | [51.0](https://drive.google.com/file/d/1xZodiAvOLGaLtMGx_btYVZIMC2VKrDhI/view?usp=sharing) / [51.4](https://drive.google.com/file/d/1bmFG9GA4bRqOEJfqXcO7nWYPwG3wSk2J/view?usp=sharing) | 451M   | [config](./upernet_beit_adapter_large_512_80k_cocostuff10k_ss.py)     | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.4/upernet_beit_adapter_large_512_80k_cocostuff10k.pth.tar) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/upernet_beit_adapter_large_512_80k_cocostuff10k_ss.log)       |
| Mask2Former | ViT-Adapter-L | [BEiT-L](https://conversationhub.blob.core.windows.net/beit-share-public/beit/beit_large_patch16_224_pt22k_ft22k.pth) | 8x2        | 40k     | 512       | [53.2](https://drive.google.com/file/d/1Buewc1n7GBAcBDXeia-QarujrDZqc_Sx/view?usp=sharing) / [54.2](https://drive.google.com/file/d/1kQgJUHDeQoO3pPY6QoXRKwyF7heT7wCJ/view?usp=sharing) | 568M   | [config](./mask2former_beit_adapter_large_512_40k_cocostuff10k_ss.py) | [ckpt](https://github.com/czczup/ViT-Adapter/releases/download/v0.2.5/mask2former_beit_adapter_large_512_40k_cocostuff10k.zip) \| [log](https://huggingface.co/czczup/ViT-Adapter/raw/main/mask2former_beit_adapter_large_512_40k_cocostuff10k_ss.log)   |
