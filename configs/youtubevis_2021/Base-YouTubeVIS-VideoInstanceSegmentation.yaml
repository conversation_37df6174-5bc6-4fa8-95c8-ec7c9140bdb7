MODEL:
  BACKBONE:
    FREEZE_AT: 0
    NAME: "build_resnet_backbone"
  WEIGHTS: "detectron2://ImageNetPretrained/torchvision/R-50.pkl"
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.120, 57.375]
  MASK_ON: True
  RESNETS:
    DEPTH: 50
    STEM_TYPE: "basic"  # not used
    STEM_OUT_CHANNELS: 64
    STRIDE_IN_1X1: False
    OUT_FEATURES: ["res2", "res3", "res4", "res5"]
    # NORM: "SyncBN"
    RES5_MULTI_GRID: [1, 1, 1]  # not used
DATASETS:
  TRAIN: ("ytvis_2021_train",)
  TEST: ("ytvis_2021_val",)
SOLVER:
  IMS_PER_BATCH: 16
  BASE_LR: 0.0001
  STEPS: (5500,)
  MAX_ITER: 8000
  WARMUP_FACTOR: 1.0
  WARMUP_ITERS: 10
  WEIGHT_DECAY: 0.05
  OPTIMIZER: "ADAMW"
  BACKBONE_MULTIPLIER: 0.1
  CLIP_GRADIENTS:
    ENABLED: True
    CLIP_TYPE: "full_model"
    CLIP_VALUE: 0.01
    NORM_TYPE: 2.0
  AMP:
    ENABLED: True
INPUT:
  MIN_SIZE_TRAIN_SAMPLING: "choice_by_clip"
  RANDOM_FLIP: "flip_by_clip"
  AUGMENTATIONS: []
  MIN_SIZE_TRAIN: (360, 480)
  MIN_SIZE_TEST: 360
  CROP:
    ENABLED: False
    TYPE: "absolute_range"
    SIZE: (600, 720)
  FORMAT: "RGB"
TEST:
  EVAL_PERIOD: 0
DATALOADER:
  FILTER_EMPTY_ANNOTATIONS: False
  NUM_WORKERS: 4
VERSION: 2
