_BASE_: ../maskformer2_R50_bs16_300k.yaml
MODEL:
  BACKBONE:
    NAME: "Dinov2_adapter"
  DINOV2:
    EMBED_DIM: 1024
    DEPTHS: 24
    NUM_HEADS: 16
    FFN_TYPE: 'mlp'
    USE_CHECKPOINT: True
    OUT_CHANNEL: 256
    PATCH_SIZE: 16
    #SCALE_FACTORS: [4.0, 2.0, 1.0, 0.5]
    #OUT_FEATURES: ["res2", "res3", "res4", "res5"]
  WEIGHTS: "/home/<USER>/rs_workspace/Mask2Former_DINOv2/checkpoints/dinov2_vitl14_pretrain_14to16final.pth"
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.120, 57.375]
USE_LAYER_DECAY: True
LR_DECAY_RATE: 0.65
OUTPUT_DIR: "/home/<USER>/rs_workspace/Mask2Former_DINOv2/seg_out/dinov2newdatabalance_adapter419_output"


SOLVER:
  IMS_PER_BATCH: 4
  MAX_ITER: 300000

  # AMP:
  #   ENABLED: True  # 1111
