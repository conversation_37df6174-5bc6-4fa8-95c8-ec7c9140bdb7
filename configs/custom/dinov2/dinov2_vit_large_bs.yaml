_BASE_: ../maskformer2_R50_bs16_300k.yaml
MODEL:
  BACKBONE:
    NAME: "DinoV2"
  DINOV2:
    EMBED_DIM: 1024
    DEPTHS: 24
    NUM_HEADS: 16
    WINDOW_SIZE: 14
    FFN_TYPE: 'mlp'
    WINDOW_BLOCK_INDEXES: [0, 1, 3, 4, 6, 7, 9, 10,]
    USE_CHECKPOINT: True
    OUT_CHANNEL: 256
    PATCH_SIZE: 16
    SCALE_FACTORS: [4.0, 2.0, 1.0, 0.5]
    OUT_FEATURES: ["res2", "res3", "res4", "res5"]
  WEIGHTS: "/home/<USER>/rs_workspace/Mask2Former_DINOv2/checkpoints/new_dinov2_vitl14_pretrain.pth"
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.120, 57.375]
USE_LAYER_DECAY: True
LR_DECAY_RATE: 0.65
OUTPUT_DIR: "/home/<USER>/rs_workspace/Mask2Former_DINOv2/dinov2newdatabalance_adapter_output"
INPUT:
  MIN_SIZE_TRAIN: !!python/object/apply:eval ["[int(x * 0.1 * 256) for x in range(5, 21)]"]  # 减小训练图像尺寸
  MAX_SIZE_TRAIN: 512

SOLVER:
  IMS_PER_BATCH: 4
  MAX_ITER: 300000

  AMP:
    ENABLED: True  # 1111
