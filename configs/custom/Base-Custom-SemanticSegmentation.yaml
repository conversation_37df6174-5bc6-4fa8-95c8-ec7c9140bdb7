MODEL:
  BACKBONE:
    FREEZE_AT: 0
    NAME: "build_resnet_backbone"
  #WEIGHTS: "/home/<USER>/rs_workspace/Mask2Former_DINOv2/configs/custom/r50.pkl"
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.120, 57.375]
  RESNETS:
    DEPTH: 50
    STEM_TYPE: "basic"  # not used
    STEM_OUT_CHANNELS: 64
    STRIDE_IN_1X1: False
    OUT_FEATURES: ["res2", "res3", "res4", "res5"]
    # NORM: "SyncBN"
    RES5_MULTI_GRID: [1, 1, 1]  # not used
DATASETS:
  TRAIN: ("custom_train",)
  TEST: ("custom_val",)
SOLVER:
  IMS_PER_BATCH: 4
  BASE_LR: 0.0001
  MAX_ITER: 300000
  WARMUP_FACTOR: 1.0
  WARMUP_ITERS: 0
  WEIGHT_DECAY: 0.05
  OPTIMIZER: "ADAMW"
  LR_SCHEDULER_NAME: "WarmupPolyLR"
  BACKBONE_MULTIPLIER: 0.1
  CLIP_GRADIENTS:
    ENABLED: True
    CLIP_TYPE: "full_model"
    CLIP_VALUE: 0.01
    NORM_TYPE: 2.0
  AMP:
    ENABLED: True
INPUT:
  MIN_SIZE_TRAIN: !!python/object/apply:eval ["[int(x * 0.1 * 2048) for x in range(5, 16)]"]
  MIN_SIZE_TRAIN_SAMPLING: "choice"
  MIN_SIZE_TEST: 720
  MAX_SIZE_TRAIN: 4096
  MAX_SIZE_TEST: 1280
  CROP:
    ENABLED: True
    TYPE: "absolute"
    SIZE: (512, 512)
    SINGLE_CATEGORY_MAX_AREA: 1.0
  COLOR_AUG_SSD: True
  SIZE_DIVISIBILITY: 512  # used in dataset mapper
  FORMAT: "RGB"
  DATASET_MAPPER_NAME: "mask_former_semantic"
OUTPUT_DIR: "seg_out/rs50final"
TEST:
  EVAL_PERIOD: 50000
DATALOADER:
  FILTER_EMPTY_ANNOTATIONS: True
  NUM_WORKERS: 4
VERSION: 2
